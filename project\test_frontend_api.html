<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend API</title>
</head>
<body>
    <h1>Test Frontend API Connection</h1>
    <button onclick="testFakeNews()">Test Fake News</button>
    <button onclick="testRealNews()">Test Real News</button>
    <div id="results"></div>

    <script>
        async function testFakeNews() {
            const text = "BREAKING: You won't believe this shocking discovery! Doctors hate this one trick!";
            await testDetection(text, "Fake News Test");
        }

        async function testRealNews() {
            const text = "According to a peer-reviewed study published in Nature, researchers found evidence.";
            await testDetection(text, "Real News Test");
        }

        async function testDetection(text, testName) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<h3>${testName}</h3><p>Testing: ${text}</p>`;
            
            try {
                const response = await fetch('http://localhost:5000/api/detect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    resultsDiv.innerHTML += `
                        <p><strong>Result:</strong> ${result.result}</p>
                        <p><strong>Confidence:</strong> ${(result.confidence * 100).toFixed(1)}%</p>
                        <p><strong>Analysis:</strong> ${result.analysis}</p>
                        <hr>
                    `;
                } else {
                    resultsDiv.innerHTML += `<p style="color: red;">Error: ${response.status} - ${response.statusText}</p><hr>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<p style="color: red;">Error: ${error.message}</p><hr>`;
            }
        }
    </script>
</body>
</html>
