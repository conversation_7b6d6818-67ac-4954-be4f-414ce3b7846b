import requests
import json

print("Testing backend directly...")

# Test fake news
fake_text = "BREAKING: You won't believe this shocking discovery! Doctors hate this one trick!"
print(f"Testing fake news: {fake_text}")

try:
    response = requests.post('http://localhost:5000/api/detect', json={'text': fake_text})
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Result: {result}")
    else:
        print(f"Error response: {response.text}")
except Exception as e:
    print(f"Error: {e}")

print("\n" + "="*50)

# Test real news
real_text = "According to a peer-reviewed study published in Nature, researchers found evidence."
print(f"Testing real news: {real_text}")

try:
    response = requests.post('http://localhost:5000/api/detect', json={'text': real_text})
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Result: {result}")
    else:
        print(f"Error response: {response.text}")
except Exception as e:
    print(f"Error: {e}")
