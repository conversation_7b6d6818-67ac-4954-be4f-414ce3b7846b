
import requests
import json

print('🧪 Testing Improved Fake News Detection:')
print('=' * 50)

# Test cases
test_cases = [
    {
        'name': 'Obvious Fake News',
        'text': 'BREAKING: You won\'t believe this shocking discovery! Doctors hate this one trick that turns water into gold!'
    },
    {
        'name': 'Legitimate News',
        'text': 'According to a peer-reviewed study published in Nature, researchers found evidence of climate change effects.'
    },
    {
        'name': 'Clickbait',
        'text': 'URGENT: This amazing discovery will change everything! Scientists are baffled!'
    },
    {
        'name': 'Academic Article',
        'text': 'University study finds correlation between exercise and mental health. The research, published in the Journal of Health Psychology, indicates positive outcomes.'
    },
    {
        'name': 'Neutral Content',
        'text': 'The weather today is sunny with a chance of rain in the afternoon.'
    }
]

for i, test in enumerate(test_cases, 1):
    print(f'{i}. {test["name"]}:')
    print(f'   Text: "{test["text"][:60]}..."')
    
    try:
        response = requests.post('http://localhost:5000/api/detect', json={'text': test['text']})
        if response.status_code == 200:
            result = response.json()
            print(f'   Result: {result["result"]}')
            print(f'   Confidence: {result["confidence"]:.1%}')
            print(f'   Analysis: {result["analysis"]}')
        else:
            print(f'   Error: {response.status_code}')
    except Exception as e:
        print(f'   Error: {e}')
    
    print()

print('✅ Testing complete!')

# Test Results:
# 1. "BREAKING: You won't believe this shocking discovery! Doctors hate this one trick"
#    → LIKELY_FAKE (95% confidence) ✅

# 2. "According to a peer-reviewed study published in Nature"  
#    → LIKELY_REAL (95% confidence) ✅

# 3. "URGENT: This amazing discovery will change everything!"
#    → LIKELY_FAKE (95% confidence) ✅

# 4. "University study finds correlation... published in Journal"
#    → LIKELY_REAL (95% confidence) ✅

# 5. "The weather today is sunny"
#    → UNCERTAIN (50% confidence) ✅

