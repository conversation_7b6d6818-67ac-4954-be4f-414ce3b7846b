
import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Authentication API
export const authAPI = {
  async register(username: string, email: string, password: string) {
    try {
      const response = await api.post('/register', {
        username,
        email,
        password,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Registration failed');
    }
  },

  async login(username: string, password: string) {
    try {
      const response = await api.post('/login', {
        username,
        password,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Login failed');
    }
  },

  async getProfile() {
    try {
      const response = await api.get('/profile');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch profile');
    }
  },
};

// Fake news detection API
export const detectionAPI = {
  async detectNews(text?: string, url?: string) {
    try {
      const response = await api.post('/detect', {
        text,
        url,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Detection failed');
    }
  },

  async getHistory() {
    try {
      const response = await api.get('/history');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch history');
    }
  },

  async getResult(resultId: number) {
    try {
      const response = await api.get(`/results/${resultId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch result');
    }
  },
};

// Legacy functions for backward compatibility
export async function submitQuestion(
  question: string,
  sessionId?: string,
  parentId?: string
) {
  // This would be used for chat functionality if needed
  console.warn('submitQuestion is deprecated, use detectionAPI.detectNews instead');
  return detectionAPI.detectNews(question);
}

export async function getSessions() {
  console.warn('getSessions is deprecated, use detectionAPI.getHistory instead');
  return detectionAPI.getHistory();
}

export async function getSession(sessionId: string) {
  console.warn('getSession is deprecated, use detectionAPI.getResult instead');
  return detectionAPI.getResult(parseInt(sessionId));
}


